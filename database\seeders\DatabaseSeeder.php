<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        // Run the UserSeeder first to create owners and tenants
        $this->call([
            UserSeeder::class,
            KosSeeder::class,
        ]);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📋 Summary:');
        $this->command->info('- Users created with sample owners and tenants');
        $this->command->info('- Kos properties created with realistic data');
        $this->command->info('- All users have password: "password"');
        $this->command->info('');
        $this->command->info('🔑 Test accounts:');
        $this->command->info('- Admin: <EMAIL> (Owner)');
        $this->command->info('- Test Owner: <EMAIL> (Owner)');
        $this->command->info('- Test Tenant: <EMAIL> (Tenant)');
    }
}
